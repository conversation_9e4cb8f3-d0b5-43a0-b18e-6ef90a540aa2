import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { ErrorBoundary } from 'react-error-boundary'
import TheError from '../../../components/Kernel/@core/TheError/TheError'
import { LOCALE_CONSTANT } from '../../../globals/utils'
import { BUISNESS_DOMAINS } from '../../../utils'
import PageRoot from '../../../utils/PageRoot'
import {
  fetchPageIdFromSlug,
  metadataProcessor,
  staticParamsProcessor,
} from '../../../utils/pageUtils'

// export const dynamicParams = false // true | false,
// export const revalidate = 3600 // seconds
const EXCLUDED_KEYS = ['lang', 'variant']
// MetaData
export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const newParam = JSON.parse(JSON.stringify(params))
  if (newParam?.slug && Array.isArray(newParam?.slug)) {
    newParam.slug.unshift('dynamic-pages')
  }
  return await metadataProcessor(newParam)
}

async function Page({ params, searchParams }: PageProps) {
  let pageId = null
  const newParam = JSON.parse(JSON.stringify(params))
  if (newParam?.slug && Array.isArray(newParam?.slug)) {
    newParam.slug.unshift('dynamic-pages')
  }
  const sortingParams = Object.entries(searchParams ?? {}).filter(
    ([key]) => !EXCLUDED_KEYS.includes(key)
  )

  if (sortingParams.length) {
    console.log('Unexpected search params:', Object.fromEntries(sortingParams))
  }
  try {
    let locale = LOCALE_CONSTANT[newParam.slug?.[0]] || 'en-CA'
    if (BUISNESS_DOMAINS['altus'] === process.env.NEXT_PUBLIC_DOMAIN) {
      if (newParam?.slug?.[newParam?.slug?.length - 1] === 'fr') {
        newParam.slug?.pop()
        locale = LOCALE_CONSTANT['fr']
      }
    }
    const fullUrl = newParam.slug?.join('/') || '/'
    pageId = await fetchPageIdFromSlug(fullUrl, locale)
    console.log(pageId, 'pageId')
  } catch (error) {
    return <TheError error={{}} />
  }
  if (!pageId) {
    return notFound()
  }
  const sortingConfig = Object.fromEntries(sortingParams)

  return (
    <ErrorBoundary FallbackComponent={TheError}>
      <PageRoot
        params={newParam}
        pageId={pageId}
        sortingConfig={sortingConfig}
      />
    </ErrorBoundary>
  )
}

// Export the component as a Next.js server component
export default Page

export async function generateStaticParams() {
  return await staticParamsProcessor()
}
